import unittest

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.tools.schemas import get_tool_definitions


class TestSchemas(unittest.TestCase):
    def test_tool_names(self):
        tools = get_tool_definitions()
        names = {t.name for t in tools}
        expected = {
            "login",
            "register_customer",
            "search_destinations",
            "search_hotels",
            "get_hotel_details",
            "get_hotel_offers",
            "create_cart",
            "get_cart",
            "create_order",
            "get_orders",
            "get_order",
            "get_bookings",
            "cancel_order",
            "get_customer_profile",
            "send_contact_message",
        }
        self.assertTrue(expected.issubset(names))


if __name__ == "__main__":
    unittest.main()

