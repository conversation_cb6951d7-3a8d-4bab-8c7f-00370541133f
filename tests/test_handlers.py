import unittest
import asyncio
import sys
import os
from typing import Any, Dict

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.tools.handlers import dispatch


class DummyClient:
    async def login(self, username: str, password: str) -> Dict[str, Any]:
        return {"ok": True, "user": username}

    async def register_customer(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        return {"registered": True, **payload}

    async def search_destinations(self, hint: str) -> Dict[str, Any]:
        return {"hint": hint}

    async def search_hotels(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        assert "destination" in payload
        return {"search": True, **payload}

    async def get_hotel_details(self, hotel_id: str, channel: str, lang: str) -> Dict[str, Any]:
        return {"hotel_id": hotel_id, "channel": channel, "lang": lang}

    async def get_hotel_offers(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        return payload

    async def create_cart(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        return payload

    async def get_cart(self, cart_id: str) -> Dict[str, Any]:
        return {"cart_id": cart_id}

    async def create_order(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        return payload

    async def get_orders(self, page: int, size: int) -> Dict[str, Any]:
        return {"page": page, "size": size}

    async def get_order(self, order_id: str) -> Dict[str, Any]:
        return {"order_id": order_id}

    async def get_bookings(self) -> Dict[str, Any]:
        return {"bookings": []}

    async def cancel_order(self, order_id: str) -> Dict[str, Any]:
        return {"cancelled": order_id}

    async def get_customer_profile(self) -> Dict[str, Any]:
        return {"profile": {}}

    async def send_contact_message(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        return payload


class TestHandlers(unittest.IsolatedAsyncioTestCase):
    async def test_search_hotels_payload(self):
        client = DummyClient()
        args = {
            "destination": {"id": "1", "group": "CITY", "display": "X", "code": "C1", "country": "", "countryCode": "BR", "state": "SP", "city": "SP", "location": {"latitude": 0.0, "longitude": 0.0}},
            "checkin": "2025-01-01",
            "checkout": "2025-01-02",
            "distribution": [{"adults": 2, "kids": []}],
        }
        out = await dispatch("search_hotels", args, client)  # should not raise
        self.assertEqual(out[0].type, "text")

    async def test_login(self):
        client = DummyClient()
        out = await dispatch("login", {"username": "u", "password": "p"}, client)
        self.assertIn("Login successful", out[0].text)


if __name__ == "__main__":
    unittest.main()

