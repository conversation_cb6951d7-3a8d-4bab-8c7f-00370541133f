import unittest
import sys
import os

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.services.hotel_service import HotelAPIClient


class TestServiceHeaders(unittest.TestCase):
    def test_headers_without_auth(self):
        c = HotelAPIClient()
        h = c._get_headers()
        self.assertEqual(h["Content-Type"], "application/json")
        self.assertEqual(h["Accept"], "application/json")
        self.assertNotIn("Authorization", h)

    def test_headers_with_auth(self):
        c = HotelAPIClient()
        c.auth_token = "abc"
        h = c._get_headers(include_auth=True)
        self.assertEqual(h.get("Authorization"), "Bearer abc")


if __name__ == "__main__":
    unittest.main()

