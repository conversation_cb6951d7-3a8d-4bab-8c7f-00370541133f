FROM python:3.12-slim

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1

WORKDIR /app

COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

COPY src ./src
COPY README.md ./
COPY run.py ./

ENV OURTRIP_BASE_URL="https://api-hml.ourtrip.com.br" \
    OURTRIP_SSL_VERIFY="true" \
    OURTRIP_HTTP_TIMEOUT_TOTAL="30" \
    OURTRIP_HTTP_TIMEOUT_CONNECT="10" \
    OURTRIP_HTTP_MAX_RETRIES="3" \
    GATEWAY_MAX_CONNECTIONS=50 \
    GATEWAY_HOST=0.0.0.0 \
    GATEWAY_PORT=3000

EXPOSE 8787
CMD ["python3", "-m", "run", "--mode", "http"]
