#!/usr/bin/env python3
"""
Legacy entrypoint for OurTrip MCP server.
This is a shim that imports and runs the main module.

With FastMCP, main() is a synchronous function that blocks and manages
its own event loop as needed.
"""

if __name__ == "__main__":
    from src.main import main
    import sys

    try:
        main()
    except KeyboardInterrupt:
        print("Server shutdown requested")
    except Exception as e:
        print(f"Server error: {e}")
        sys.exit(1)
