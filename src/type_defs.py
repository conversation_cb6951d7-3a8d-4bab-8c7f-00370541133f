from __future__ import annotations

from typing import List, Literal, Optional, TypedDict


class Location(TypedDict):
    latitude: float
    longitude: float


class Destination(TypedDict):
    id: str
    group: Literal["CITY", "HOTEL"]
    display: str
    code: str
    country: Optional[str]
    countryCode: str
    state: str
    city: str
    location: Location


class DistributionItem(TypedDict):
    adults: int
    kids: List[int]


class SearchFilters(TypedDict, total=False):
    mealPlans: List[str]
    stars: List[int]
    acommodationTypes: List[str]
    amenities: List[str]
    price: float | None
    hotelName: str | None
    cancellationPolicyRefundable: bool
    tripcash: List[str]


class SearchHotelsArgs(TypedDict):
    destination: Destination
    checkin: str
    checkout: str
    distribution: List[DistributionItem]
    page: int
    limit: int
    currency: str
    channel: str
    lang: Literal["PT_BR", "PT_PT", "EN_US", "ES_ES"]
    searchId: str
    sorting: str
    filters: SearchFilters


class GetHotelDetailsArgs(TypedDict):
    hotel_id: str
    channel: str
    lang: Literal["PT_BR", "PT_PT", "EN_US", "ES_ES"]


class GetHotelOffersArgs(TypedDict):
    hotelId: str
    checkin: str
    checkout: str
    distribution: List[DistributionItem]
    channel: str
    currency: str
    lang: Literal["PT_BR", "PT_PT", "EN_US", "ES_ES"]


class CreateCartArgs(TypedDict):
    searchToken: str
    offerTokens: List[str]
    lang: Literal["PT_BR", "PT_PT", "EN_US", "ES_ES"]
    pos: str


class CreateOrderGuest(TypedDict):
    name: str
    surname: str
    document: str
    age: int
    child: bool


class CreateOrderGuestsByIndex(TypedDict):
    index: int
    guests: List[CreateOrderGuest]


class Identification(TypedDict, total=False):
    documentType: str
    documentNumber: str


class Payer(TypedDict, total=False):
    email: str
    firstName: str
    lastName: str
    identification: Identification


class PaymentInfo(TypedDict):
    method: Literal["PIX", "CREDIT_CARD", "EXTERNAL"]
    tripcashApplied: bool
    payer: Payer


class CreateOrderArgs(TypedDict):
    cartId: str
    guests: List[CreateOrderGuestsByIndex]
    paymentInfo: PaymentInfo


class GetOrdersArgs(TypedDict, total=False):
    page: int
    size: int


class OrderIdArg(TypedDict):
    order_id: str


class CartIdArg(TypedDict):
    cart_id: str


class ContactMessageArgs(TypedDict, total=False):
    name: str
    mail: str
    message: str
    lang: Literal["PT_BR", "PT_PT", "EN_US", "ES_ES"]

