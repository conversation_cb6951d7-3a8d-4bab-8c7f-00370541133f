from __future__ import annotations

from typing import Any, Dict, List, Callable

from fastmcp import FastMCP

from .logging_config import get_logger
from .services.hotel_service import HotelAPIClient
from .tools.schemas import get_tool_definitions
from .tools.handlers import dispatch

logger = get_logger()

# FastMCP server instance
mcp = FastMCP(name="hotel-booking")
client = HotelAPIClient()


def _make_tool(tool_name: str, description: str | None = None) -> Callable[[Dict[str, Any]], "str | dict | list[str]"]:
    """Create a FastMCP tool wrapper that delegates to our existing dispatch function.

    We intentionally accept a single "arguments" object for compatibility with the
    existing JSON schemas and handlers. The handler returns MCP TextContent blocks;
    we convert them to a single text payload for FastMCP.
    """

    @mcp.tool(name=tool_name, description=description or f"Execute {tool_name}")
    async def _tool(arguments: Dict[str, Any]) -> str:
        try:
            async with client:
                contents = await dispatch(tool_name, arguments, client)
                # Join any text content blocks into a single string
                texts: List[str] = []
                for c in contents:
                    text = getattr(c, "text", None)
                    if isinstance(text, str):
                        texts.append(text)
                return "\n\n".join(texts) if texts else ""
        except Exception as e:
            logger.error(f"Tool '{tool_name}' failed: {e}")
            # Return a simple error string; FastMCP will surface it to the client
            return f"Error executing {tool_name}: {str(e)}"

    return _tool


# Register all tools defined in our existing schema module
for _t in get_tool_definitions():
    try:
        _make_tool(_t.name, getattr(_t, "description", None))
    except Exception as e:
        logger.error(f"Failed to register tool '{getattr(_t, 'name', '?')}': {e}")
