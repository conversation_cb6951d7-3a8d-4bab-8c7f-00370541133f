from __future__ import annotations

from typing import Any, Dict, Optional
import aiohttp

from ..http_client import AsyncHTTPClient
from ..logging_config import get_logger

logger = get_logger()


class HotelAPIClient:
    def __init__(self, base_url: Optional[str] = None):
        self.http = AsyncHTTPClient(base_url)
        self.auth_token: Optional[str] = None

    async def __aenter__(self) -> "HotelAPIClient":
        await self.http.__aenter__()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        await self.http.__aexit__(exc_type, exc_val, exc_tb)

    def _get_headers(self, include_auth: bool = False) -> Dict[str, str]:
        headers = {"Content-Type": "application/json", "Accept": "application/json"}
        if include_auth and self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
        return headers

    # Auth
    async def login(self, username: str, password: str) -> Dict[str, Any]:
        try:
            url = f"{self.http.base_url}/auth"
            if not self.http.session:
                raise RuntimeError("HTTP session not initialized")
            async with self.http.session.post(url, auth=aiohttp.BasicAuth(username, password)) as response:
                response.raise_for_status()
                result = await response.json()
                if "token" in result:
                    self.auth_token = result["token"]
                return result
        except Exception as e:
            logger.error(f"Login failed: {e}")
            raise

    async def register_customer(self, customer_data: Dict[str, Any]) -> Dict[str, Any]:
        return await self.http.request("POST", "/customer", data=customer_data, headers=self._get_headers())

    async def get_customer_profile(self) -> Dict[str, Any]:
        return await self.http.request("GET", "/customer", headers=self._get_headers(include_auth=True))

    # Search
    async def search_destinations(self, hint: str) -> Dict[str, Any]:
        return await self.http.request("GET", "/hotel/autocomplete", params={"hint": hint}, headers=self._get_headers())

    async def get_destination_details(self, group: str, code: str) -> Dict[str, Any]:
        return await self.http.request("GET", f"/hotel/autocomplete/destination/{group}/{code}", headers=self._get_headers())

    async def search_hotels(self, search_payload: Dict[str, Any]) -> Dict[str, Any]:
        return await self.http.request("POST", "/hotel/availability", data=search_payload, headers=self._get_headers())

    # Hotel
    async def get_hotel_details(self, hotel_id: str, channel: str, lang: str = "en") -> Dict[str, Any]:
        return await self.http.request(
            "GET", f"/hotel/content/{lang}/{hotel_id}", params={"channel": channel}, headers=self._get_headers()
        )

    async def get_hotel_offers(self, offer_payload: Dict[str, Any]) -> Dict[str, Any]:
        return await self.http.request("POST", "/hotel/offers", data=offer_payload, headers=self._get_headers())

    # Cart
    async def create_cart(self, cart_data: Dict[str, Any]) -> Dict[str, Any]:
        return await self.http.request("POST", "/cart", data=cart_data, headers=self._get_headers())

    async def get_cart(self, cart_id: str) -> Dict[str, Any]:
        return await self.http.request("GET", f"/cart/{cart_id}", headers=self._get_headers(include_auth=True))

    # Order
    async def create_order(self, order_payload: Dict[str, Any]) -> Dict[str, Any]:
        return await self.http.request("POST", "/order", data=order_payload, headers=self._get_headers(include_auth=True))

    async def get_order(self, order_id: str) -> Dict[str, Any]:
        return await self.http.request("GET", f"/order/{order_id}", headers=self._get_headers(include_auth=True))

    async def get_orders(self, page: int = 0, size: int = 10) -> Dict[str, Any]:
        return await self.http.request(
            "GET", "/order", params={"page": page, "size": size}, headers=self._get_headers(include_auth=True)
        )

    async def cancel_order(self, order_id: str) -> Dict[str, Any]:
        return await self.http.request("POST", "/order/cancel", data={"id": order_id}, headers=self._get_headers(include_auth=True))

    # Booking
    async def get_bookings(self) -> Dict[str, Any]:
        return await self.http.request("GET", "/hotel/booking", headers=self._get_headers(include_auth=True))

    # Tripcash
    async def get_tripcash(self, lang: str = "en") -> Dict[str, Any]:
        return await self.http.request("GET", "/tripcash", params={"lang": lang}, headers=self._get_headers(include_auth=True))

    # Utility
    async def send_contact_message(self, contact_data: Dict[str, Any]) -> Dict[str, Any]:
        return await self.http.request("POST", "/contact/sendMessage", data=contact_data, headers=self._get_headers())

