from __future__ import annotations

import json
from typing import Any, Dict, List

from mcp import types as mcp_types

from ..services.hotel_service import HotelAPIClient
from ..type_defs import (
    CartIdArg,
    ContactMessageArgs,
    CreateCartArgs,
    CreateOrderArgs,
    GetHotelDetailsArgs,
    GetHotelOffersArgs,
    GetOrdersArgs,
    OrderIdArg,
    SearchHotelsArgs,
)


def _text(payload: Any) -> mcp_types.TextContent:
    return mcp_types.TextContent(type="text", text=json.dumps(payload, indent=2))


async def dispatch(name: str, arguments: Dict[str, Any], client: HotelAPIClient) -> List[mcp_types.TextContent]:
    if name == "login":
        result = await client.login(arguments["username"], arguments["password"])
        return [mcp_types.TextContent(type="text", text=f"Login successful. Authentication token stored.\n{json.dumps(result, indent=2)}")]

    if name == "register_customer":
        result = await client.register_customer(arguments)
        return [mcp_types.TextContent(type="text", text=f"Customer registered successfully:\n{json.dumps(result, indent=2)}")]

    if name == "search_destinations":
        result = await client.search_destinations(arguments["hint"]) 
        return [mcp_types.TextContent(type="text", text=f"Destinations found:\n{json.dumps(result, indent=2)}")]

    if name == "search_hotels":
        args: SearchHotelsArgs = {
            "destination": arguments["destination"],
            "checkin": arguments["checkin"],
            "checkout": arguments["checkout"],
            "distribution": arguments["distribution"],
            "page": arguments.get("page", 0),
            "limit": arguments.get("limit", 20),
            "currency": arguments.get("currency", "BRL"),
            "searchId": arguments.get("searchId", ""),
            "sorting": arguments.get("sorting", "relevance"),
            "filters": arguments.get("filters", {}),
            "channel": arguments.get("channel", "SITE"),
            "lang": arguments.get("lang", "PT_BR"),
        }
        result = await client.search_hotels(args)
        return [mcp_types.TextContent(type="text", text=f"Hotels found:\n{json.dumps(result, indent=2)}")]

    if name == "get_hotel_details":
        args: GetHotelDetailsArgs = {
            "hotel_id": arguments["hotel_id"],
            "channel": arguments.get("channel", "web"),
            "lang": arguments.get("lang", "PT_BR"),
        }
        result = await client.get_hotel_details(args["hotel_id"], args["channel"], args["lang"]) 
        return [mcp_types.TextContent(type="text", text=f"Hotel details:\n{json.dumps(result, indent=2)}")]

    if name == "get_hotel_offers":
        args: GetHotelOffersArgs = arguments  # trust schema
        result = await client.get_hotel_offers(args)
        return [mcp_types.TextContent(type="text", text=f"Hotel offers:\n{json.dumps(result, indent=2)}")]

    if name == "create_cart":
        args: CreateCartArgs = arguments
        result = await client.create_cart(args)
        return [mcp_types.TextContent(type="text", text=f"Cart created:\n{json.dumps(result, indent=2)}")]

    if name == "get_cart":
        args: CartIdArg = {"cart_id": arguments["cart_id"]}
        result = await client.get_cart(args["cart_id"]) 
        return [mcp_types.TextContent(type="text", text=f"Cart details:\n{json.dumps(result, indent=2)}")]

    if name == "create_order":
        args: CreateOrderArgs = arguments
        result = await client.create_order(args)
        return [mcp_types.TextContent(type="text", text=f"Order created:\n{json.dumps(result, indent=2)}")]

    if name == "get_orders":
        args: GetOrdersArgs = {"page": arguments.get("page", 0), "size": arguments.get("size", 10)}
        result = await client.get_orders(args.get("page", 0), args.get("size", 10))
        return [mcp_types.TextContent(type="text", text=f"Orders:\n{json.dumps(result, indent=2)}")]

    if name == "get_order":
        args: OrderIdArg = {"order_id": arguments["order_id"]}
        result = await client.get_order(args["order_id"]) 
        return [mcp_types.TextContent(type="text", text=f"Order details:\n{json.dumps(result, indent=2)}")]

    if name == "get_bookings":
        result = await client.get_bookings()
        return [mcp_types.TextContent(type="text", text=f"Bookings:\n{json.dumps(result, indent=2)}")]

    if name == "cancel_order":
        args: OrderIdArg = {"order_id": arguments["order_id"]}
        result = await client.cancel_order(args["order_id"]) 
        return [mcp_types.TextContent(type="text", text=f"Order cancelled:\n{json.dumps(result, indent=2)}")]

    if name == "get_customer_profile":
        result = await client.get_customer_profile()
        return [mcp_types.TextContent(type="text", text=f"Customer profile:\n{json.dumps(result, indent=2)}")]

    if name == "send_contact_message":
        args: ContactMessageArgs = arguments
        result = await client.send_contact_message(args)
        return [mcp_types.TextContent(type="text", text=f"Contact message sent:\n{json.dumps(result, indent=2)}")]

    return [mcp_types.TextContent(type="text", text=f"Unknown tool: {name}")]

