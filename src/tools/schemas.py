from __future__ import annotations
from mcp import types as mcp_types

def get_tool_definitions() -> list[mcp_types.Tool]:
    return [
        mcp_types.Tool(
            name="login",
            description="Login to the hotel booking system",
            inputSchema={
                "type": "object",
                "properties": {
                    "username": {"type": "string", "description": "Username or email"},
                    "password": {"type": "string", "description": "User password"},
                },
                "required": ["username", "password"],
            },
        ),
        mcp_types.Tool(
            name="register_customer",
            description="Register a new customer account",
            inputSchema={
                "type": "object",
                "properties": {
                    "name": {"type": "string", "description": "First name"},
                    "surname": {"type": "string", "description": "Last name"},
                    "email": {"type": "string", "description": "Email address"},
                    "documentNumber": {"type": "string", "description": "Document number"},
                    "documentType": {"type": "string", "description": "Document type (CPF, RG, etc.)"},
                    "phone": {
                        "type": "object",
                        "properties": {"areaCode": {"type": "string"}, "number": {"type": "string"}},
                    },
                    "password": {"type": "string", "description": "Account password"},
                    "lang": {
                        "type": "string",
                        "description": "Language preference",
                        "enum": ["PT_BR", "PT_PT", "EN_US", "ES_ES"],
                        "default": "PT_BR",
                    },
                },
                "required": ["email", "documentNumber", "documentType"],
            },
        ),
        mcp_types.Tool(
            name="search_destinations",
            description="Search for destinations with autocomplete",
            inputSchema={
                "type": "object",
                "properties": {"hint": {"type": "string", "description": "Search term for destinations"}},
                "required": ["hint"],
            },
        ),
        mcp_types.Tool(
            name="search_hotels",
            description="Search for available hotels",
            inputSchema={
                "type": "object",
                "properties": {
                    "destination": {
                        "type": "object",
                        "properties": {
                            "id": {"type": "string"},
                            "group": {"type": "string", "enum": ["CITY", "HOTEL"]},
                            "display": {"type": "string"},
                            "code": {"type": "string"},
                            "country": {"type": "string"},
                            "countryCode": {"type": "string"},
                            "state": {"type": "string"},
                            "city": {"type": "string"},
                            "location": {
                                "type": "object",
                                "properties": {"latitude": {"type": "number"}, "longitude": {"type": "number"}},
                                "required": ["latitude", "longitude"],
                            },
                        },
                        "required": ["id", "group", "display", "code", "countryCode", "state", "city"],
                    },
                    "checkin": {"type": "string", "description": "Check-in date (YYYY-MM-DD)"},
                    "checkout": {"type": "string", "description": "Check-out date (YYYY-MM-DD)"},
                    "distribution": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "adults": {"type": "integer", "minimum": 1},
                                "kids": {"type": "array", "items": {"type": "integer"}, "default": "[]"},
                            },
                            "required": ["adults", "kids"],
                        },
                    },
                    "filters": {
                        "type": "object",
                        "properties": {
                            "mealPlans": {"type": "array", "items": {"type": "string"}, "default": "[]"},
                            "stars": {"type": "array", "items": {"type": "integer"}, "default": "[]"},
                            "acommodationTypes": {"type": "array", "items": {"type": "string"}, "default": "[]"},
                            "amenities": {"type": "array", "items": {"type": "string"}, "default": "[]"},
                            "price": {"type": ["null", "number"]},
                            "hotelName": {"type": ["null", "string"]},
                            "cancellationPolicyRefundable": {"type": ["boolean"], "default": "false"},
                            "tripcash": {"type": ["array"], "items": {"type": "string"}, "default": "[]"},
                        },
                        "required": [
                            "mealPlans",
                            "stars",
                            "acommodationTypes",
                            "amenities",
                            "price",
                            "hotelName",
                            "cancellationPolicyRefundable",
                            "tripcash",
                        ],
                    },
                    "page": {"type": "integer", "default": 0},
                    "limit": {"type": "integer", "default": 20},
                    "currency": {"type": "string", "default": "BRL"},
                    "channel": {"type": "string", "default": "SITE"},
                    "lang": {"type": "string", "enum": ["PT_BR", "PT_PT", "EN_US", "ES_ES"], "default": "PT_BR"},
                    "searchId": {"type": "string", "description": "Unique search ID", "default": ""},
                    "sorting": {"type": "string", "default": "relevance"},
                },
                "required": [
                    "destination",
                    "checkin",
                    "checkout",
                    "distribution",
                    "page",
                    "limit",
                    "currency",
                    "channel",
                    "lang",
                ],
            },
        ),
        mcp_types.Tool(
            name="get_hotel_details",
            description="Get detailed information about a specific hotel",
            inputSchema={
                "type": "object",
                "properties": {
                    "hotel_id": {"type": "string", "description": "Hotel ID"},
                    "channel": {"type": "string", "default": "web"},
                    "lang": {"type": "string", "enum": ["PT_BR", "PT_PT", "EN_US", "ES_ES"], "default": "PT_BR"},
                },
                "required": ["hotel_id"],
            },
        ),
        mcp_types.Tool(
            name="get_hotel_offers",
            description="Get available offers for a specific hotel",
            inputSchema={
                "type": "object",
                "properties": {
                    "hotelId": {"type": "string"},
                    "checkin": {"type": "string", "description": "Check-in date (YYYY-MM-DD)"},
                    "checkout": {"type": "string", "description": "Check-out date (YYYY-MM-DD)"},
                    "distribution": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "adults": {"type": "integer", "minimum": 1},
                                "kids": {"type": "array", "items": {"type": "integer"}},
                            },
                        },
                    },
                    "channel": {"type": "string", "default": "web"},
                    "currency": {"type": "string", "default": "BRL"},
                    "lang": {"type": "string", "enum": ["PT_BR", "PT_PT", "EN_US", "ES_ES"], "default": "PT_BR"},
                },
                "required": ["hotelId", "checkin", "checkout", "distribution"],
            },
        ),
        mcp_types.Tool(
            name="create_cart",
            description="Create a shopping cart with selected offers",
            inputSchema={
                "type": "object",
                "properties": {
                    "searchToken": {"type": "string", "description": "Token from search results"},
                    "offerTokens": {"type": "array", "items": {"type": "string"}, "description": "List of offer tokens to add to cart"},
                    "lang": {"type": "string", "enum": ["PT_BR", "PT_PT", "EN_US", "ES_ES"], "default": "PT_BR"},
                    "pos": {"type": "string", "description": "Point of sale"},
                },
                "required": ["searchToken", "offerTokens"],
            },
        ),
        mcp_types.Tool(
            name="get_cart",
            description="Get cart details and pricing",
            inputSchema={
                "type": "object",
                "properties": {"cart_id": {"type": "string", "description": "Cart ID"}},
                "required": ["cart_id"],
            },
        ),
        mcp_types.Tool(
            name="create_order",
            description="Create an order from cart (requires authentication)",
            inputSchema={
                "type": "object",
                "properties": {
                    "cartId": {"type": "string"},
                    "guests": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "index": {"type": "integer"},
                                "guests": {
                                    "type": "array",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "name": {"type": "string"},
                                            "surname": {"type": "string"},
                                            "document": {"type": "string"},
                                            "age": {"type": "integer"},
                                            "child": {"type": "boolean"},
                                        },
                                        "required": ["name", "surname", "document", "child"],
                                    },
                                },
                            },
                        },
                    },
                    "paymentInfo": {
                        "type": "object",
                        "properties": {
                            "method": {"type": "string", "enum": ["PIX", "CREDIT_CARD", "EXTERNAL"]},
                            "tripcashApplied": {"type": "boolean", "default": "false"},
                            "payer": {
                                "type": "object",
                                "properties": {
                                    "email": {"type": "string"},
                                    "firstName": {"type": "string"},
                                    "lastName": {"type": "string"},
                                    "identification": {
                                        "type": "object",
                                        "properties": {"documentType": {"type": "string"}, "documentNumber": {"type": "string"}},
                                    },
                                },
                            },
                        },
                        "required": ["method", "payer"],
                    },
                },
                "required": ["cartId", "guests", "paymentInfo"],
            },
        ),
        mcp_types.Tool(
            name="get_orders",
            description="Get user's order history (requires authentication)",
            inputSchema={
                "type": "object",
                "properties": {"page": {"type": "integer", "default": 0}, "size": {"type": "integer", "default": 10}},
            },
        ),
        mcp_types.Tool(
            name="get_order",
            description="Get specific order details (requires authentication)",
            inputSchema={
                "type": "object",
                "properties": {"order_id": {"type": "string", "description": "Order ID"}},
                "required": ["order_id"],
            },
        ),
        mcp_types.Tool(
            name="get_bookings",
            description="Get user's hotel bookings (requires authentication)",
            inputSchema={"type": "object", "properties": {}},
        ),
        mcp_types.Tool(
            name="cancel_order",
            description="Cancel an order (requires authentication)",
            inputSchema={
                "type": "object",
                "properties": {"order_id": {"type": "string", "description": "Order ID to cancel"}},
                "required": ["order_id"],
            },
        ),
        mcp_types.Tool(
            name="get_customer_profile",
            description="Get current customer profile (requires authentication)",
            inputSchema={"type": "object", "properties": {}},
        ),
        mcp_types.Tool(
            name="send_contact_message",
            description="Send a contact message",
            inputSchema={
                "type": "object",
                "properties": {
                    "name": {"type": "string"},
                    "mail": {"type": "string"},
                    "message": {"type": "string"},
                    "lang": {"type": "string", "enum": ["PT_BR", "PT_PT", "EN_US", "ES_ES"], "default": "EN_US"},
                },
                "required": ["name", "mail", "message"],
            },
        ),
    ]

