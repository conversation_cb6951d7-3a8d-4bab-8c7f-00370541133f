from __future__ import annotations

import argparse
import os

from .logging_config import get_logger
from .server import mcp

logger = get_logger()


def main() -> None:
    """Start the OurTrip MCP server using FastMCP with selectable transport.

    Modes:
      - stdio (default)
      - http (aka streamable-http)
      - sse

    You can set the mode via CLI: --mode stdio|http|sse
    Or via env var OURTRIP_MCP_MODE / MCP_MODE.
    Host/port apply to http and sse modes and can be customized via
    --host/--port or env vars MCP_HOST/MCP_PORT.
    """
    parser = argparse.ArgumentParser(description="OurTrip MCP server")
    parser.add_argument("--mode", dest="mode", default=None, help="Transport: stdio|http|sse")
    parser.add_argument("--host", dest="host", default=None, help="Host for http/sse (default 0.0.0.0)")
    parser.add_argument("--port", dest="port", type=int, default=None, help="Port for http/sse (default 3000)")
    args = parser.parse_args()

    # Resolve mode with precedence: CLI > env (OURTRIP_MCP_MODE/MCP_MODE) > default
    mode = (args.mode or os.getenv("OURTRIP_MCP_MODE") or os.getenv("MCP_MODE") or "stdio").lower()

    # Normalize synonyms
    if mode in {"streamable-http", "http", "http-stream", "stream"}:
        transport = "http"
    elif mode in {"sse"}:
        transport = "sse"
    else:
        transport = "stdio"

    host = args.host or os.getenv("MCP_HOST") or "0.0.0.0"
    port_str = os.getenv("MCP_PORT")
    port_env = int(port_str) if port_str and port_str.isdigit() else None
    port = args.port or port_env or 3000

    if transport == "stdio":
        logger.info("OurTrip MCP server starting (stdio) via FastMCP...")
        mcp.run(transport="stdio")
    elif transport == "http":
        logger.info(f"OurTrip MCP server starting (HTTP streaming) on {host}:{port} via FastMCP...")
        mcp.run(transport="http", host=host, port=port)
    elif transport == "sse":
        logger.info(f"OurTrip MCP server starting (SSE) on {host}:{port} via FastMCP...")
        mcp.run(transport="sse", host=host, port=port)
    else:
        logger.warning(f"Unknown mode '{mode}', defaulting to stdio")
        mcp.run(transport="stdio")

    logger.info("OurTrip MCP server stopped.")
