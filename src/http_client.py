from __future__ import annotations

import asyncio
import ssl
from typing import Any, Dict, Optional

import aiohttp
import certifi

from .config import HTTP_CONFIG
from .logging_config import get_logger

logger = get_logger()


def build_ssl_context(verify: bool) -> ssl.SSLContext:
    # Start with default context
    try:
        context = ssl.create_default_context(cafile=certifi.where())
    except Exception:
        context = ssl.create_default_context()

    if not verify:
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
    return context


class AsyncHTTPClient:
    def __init__(self, base_url: str | None = None, verify_ssl: Optional[bool] = None):
        self.base_url = (base_url or HTTP_CONFIG.base_url).rstrip("/")
        self.verify_ssl = HTTP_CONFIG.ssl_verify if verify_ssl is None else verify_ssl
        self.session: Optional[aiohttp.ClientSession] = None

    async def __aenter__(self) -> "AsyncHTTPClient":
        timeout = aiohttp.ClientTimeout(
            total=HTTP_CONFIG.timeout_total, connect=HTTP_CONFIG.timeout_connect
        )
        connector = aiohttp.TCPConnector(
            ssl=build_ssl_context(self.verify_ssl),
            limit=10,
            limit_per_host=5,
            enable_cleanup_closed=True,
        )
        self.session = aiohttp.ClientSession(
            connector=connector, timeout=timeout, headers={"User-Agent": "Hotel-MCP-Server/1.0"}
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        if self.session:
            await self.session.close()

    async def request(
        self,
        method: str,
        endpoint: str,
        *,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        if not self.session:
            raise RuntimeError("HTTP session not initialized")

        url = f"{self.base_url}{endpoint}"
        attempt = 0
        last_exc: Exception | None = None
        while attempt < HTTP_CONFIG.max_retries:
            attempt += 1
            try:
                logger.info(f"HTTP {method} {url} (attempt {attempt})")
                async with self.session.request(
                    method,
                    url,
                    json=data if method != "GET" else None,
                    params=params,
                    headers=headers,
                    ssl=None,
                ) as response:
                    logger.info(f"Response status: {response.status}")
                    if response.status >= 400:
                        text = await response.text()
                        logger.error(f"API error {response.status}: {text}")
                        response.raise_for_status()
                    return await response.json()
            except Exception as e:
                last_exc = e
                if attempt >= HTTP_CONFIG.max_retries:
                    break
                logger.warning(f"Request failed: {e}. Retrying...")
                await asyncio.sleep(1)
        assert last_exc is not None
        raise last_exc

