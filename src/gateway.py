from __future__ import annotations

import asyncio
import os
import sys
from typing import Optional

from aiohttp import web, WSMsgType

from .logging_config import get_logger

logger = get_logger()


async def spawn_mcp_process() -> asyncio.subprocess.Process:
    """
    Spawn the MCP stdio server as a subprocess and return the process handle.
    We execute the current Python interpreter with module src.main to ensure
    the same environment and dependencies inside the container.
    """
    # Use the legacy shim to actually run src.main
    cmd = [sys.executable, "run.py"]
    logger.info(f"Spawning MCP subprocess: {' '.join(cmd)}")
    proc = await asyncio.create_subprocess_exec(
        *cmd,
        stdin=asyncio.subprocess.PIPE,
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE,
    )
    return proc


async def ws_handler(request: web.Request) -> web.StreamResponse:
    # Token authentication (optional):
    # - If GATEWAY_TOKEN is set, require Authorization: Bearer <token> or ?token=<token>
    required_token = os.getenv("GATEWAY_TOKEN")
    if required_token:
        auth_header = request.headers.get("Authorization", "")
        supplied: Optional[str] = None
        if auth_header.startswith("Bearer "):
            supplied = auth_header[7:]
        else:
            supplied = request.query.get("token")
        if supplied != required_token:
            return web.Response(status=401, text="Unauthorized")

    # Connection limiting
    app = request.app
    app["metrics"]["attempts"] += 1
    if app.get("current_connections", 0) >= app.get("max_connections", 50):
        app["metrics"]["rejected"] += 1
        return web.Response(status=503, text="Too many connections")
    app["current_connections"] = app.get("current_connections", 0) + 1
    app["metrics"]["accepted"] += 1

    ws = web.WebSocketResponse(heartbeat=30)
    await ws.prepare(request)

    proc: Optional[asyncio.subprocess.Process] = None

    try:
        proc = await spawn_mcp_process()

        async def ws_to_proc() -> None:
            try:
                async for msg in ws:
                    if msg.type == WSMsgType.TEXT:
                        data = msg.data
                        # Forward text messages as a line to MCP stdio
                        assert proc is not None and proc.stdin is not None
                        proc.stdin.write((data + "\n").encode())
                        await proc.stdin.drain()
                    elif msg.type == WSMsgType.BINARY:
                        # If a client sends binary, forward as-is
                        assert proc is not None and proc.stdin is not None
                        proc.stdin.write(msg.data)
                        await proc.stdin.drain()
                    elif msg.type in {WSMsgType.CLOSE, WSMsgType.CLOSING, WSMsgType.CLOSED}:
                        break
                    elif msg.type == WSMsgType.ERROR:
                        logger.error(f"WebSocket error: {ws.exception()}")
                        break
            except asyncio.CancelledError:
                raise
            except Exception as e:
                logger.error(f"ws_to_proc failed: {e}")

        async def proc_to_ws() -> None:
            try:
                assert proc is not None and proc.stdout is not None
                # Read line-delimited JSON from MCP and forward to WS as text
                while True:
                    line = await proc.stdout.readline()
                    if not line:
                        break
                    await ws.send_str(line.decode(errors="ignore").rstrip("\n"))
            except asyncio.CancelledError:
                raise
            except Exception as e:
                logger.error(f"proc_to_ws failed: {e}")

        async def stderr_logger() -> None:
            try:
                assert proc is not None and proc.stderr is not None
                while True:
                    line = await proc.stderr.readline()
                    if not line:
                        break
                    logger.error(line.decode(errors="ignore").rstrip())
            except asyncio.CancelledError:
                raise
            except Exception as e:
                logger.error(f"stderr_logger failed: {e}")

        t_ws = asyncio.create_task(ws_to_proc())
        t_proc = asyncio.create_task(proc_to_ws())
        t_err = asyncio.create_task(stderr_logger())

        done, pending = await asyncio.wait({t_ws, t_proc}, return_when=asyncio.FIRST_COMPLETED)
        for t in pending:
            t.cancel()
        await asyncio.gather(*pending, return_exceptions=True)
        # Let stderr logger wind down
        t_err.cancel()
        await asyncio.gather(t_err, return_exceptions=True)

        return ws

    finally:
        if proc is not None:
            try:
                if proc.stdin and not proc.stdin.is_closing():
                    proc.stdin.close()
                # Give the process a moment to exit gracefully
                try:
                    await asyncio.wait_for(proc.wait(), timeout=2)
                except asyncio.TimeoutError:
                    proc.kill()
            except Exception:
                pass
        await ws.close()
        # Decrement connection counter
        app = request.app
        if app.get("current_connections"):
            app["current_connections"] -= 1


async def healthz(_: web.Request) -> web.Response:
    return web.json_response({"status": "ok"})


async def metrics(request: web.Request) -> web.Response:
    app = request.app
    data = {
        "current_connections": app.get("current_connections", 0),
        "max_connections": app.get("max_connections", 0),
        "attempts": app.get("metrics", {}).get("attempts", 0),
        "accepted": app.get("metrics", {}).get("accepted", 0),
        "rejected": app.get("metrics", {}).get("rejected", 0),
    }
    return web.json_response(data)


async def http_stream_handler(request: web.Request) -> web.StreamResponse:
    # Auth
    required_token = os.getenv("GATEWAY_TOKEN")
    if required_token:
        auth_header = request.headers.get("Authorization", "")
        supplied: Optional[str] = None
        if auth_header.startswith("Bearer "):
            supplied = auth_header[7:]
        else:
            supplied = request.query.get("token")
        if supplied != required_token:
            return web.Response(status=401, text="Unauthorized")

    # Connection limiting
    app = request.app
    app["metrics"]["attempts"] += 1
    if app.get("current_connections", 0) >= app.get("max_connections", 50):
        app["metrics"]["rejected"] += 1
        return web.Response(status=503, text="Too many connections")
    app["current_connections"] = app.get("current_connections", 0) + 1
    app["metrics"]["accepted"] += 1

    # Prepare streaming response
    resp = web.StreamResponse(status=200, headers={"Content-Type": "application/json"})
    await resp.prepare(request)

    proc: Optional[asyncio.subprocess.Process] = None

    async def request_to_proc() -> None:
        try:
            assert proc is not None and proc.stdin is not None
            last_byte_newline = False
            async for chunk in request.content.iter_chunked(65536):
                if not chunk:
                    break
                proc.stdin.write(chunk)
                await proc.stdin.drain()
                last_byte_newline = chunk[-1:] == b"\n"
            # ensure newline termination for line-oriented MCP stdio
            if not last_byte_newline:
                proc.stdin.write(b"\n")
                await proc.stdin.drain()
            # close stdin when request body ends
            if not proc.stdin.is_closing():
                proc.stdin.close()
        except asyncio.CancelledError:
            raise
        except Exception as e:
            logger.error(f"request_to_proc failed: {e}")

    async def proc_to_resp() -> None:
        try:
            assert proc is not None and proc.stdout is not None
            while True:
                chunk = await proc.stdout.read(16384)
                if not chunk:
                    break
                await resp.write(chunk)
                await resp.drain()
        except asyncio.CancelledError:
            raise
        except Exception as e:
            logger.error(f"proc_to_resp failed: {e}")

    async def stderr_logger() -> None:
        try:
            assert proc is not None and proc.stderr is not None
            while True:
                line = await proc.stderr.readline()
                if not line:
                    break
                logger.error(line.decode(errors="ignore").rstrip())
        except asyncio.CancelledError:
            raise
        except Exception as e:
            logger.error(f"stderr_logger failed: {e}")

    try:
        proc = await spawn_mcp_process()
        t_in = asyncio.create_task(request_to_proc())
        t_out = asyncio.create_task(proc_to_resp())
        t_err = asyncio.create_task(stderr_logger())
        done, pending = await asyncio.wait({t_in, t_out}, return_when=asyncio.FIRST_COMPLETED)
        for t in pending:
            t.cancel()
        await asyncio.gather(*pending, return_exceptions=True)
        t_err.cancel()
        await asyncio.gather(t_err, return_exceptions=True)
    finally:
        # Cleanup
        try:
            await resp.write_eof()
        except Exception:
            pass
        if proc is not None:
            try:
                if proc.stdin and not proc.stdin.is_closing():
                    proc.stdin.close()
                try:
                    await asyncio.wait_for(proc.wait(), timeout=2)
                except asyncio.TimeoutError:
                    proc.kill()
            except Exception:
                pass
        # decrement connection
        if app.get("current_connections"):
            app["current_connections"] -= 1

    return resp


async def sse_handler(request: web.Request) -> web.StreamResponse:
    # Auth
    required_token = os.getenv("GATEWAY_TOKEN")
    if required_token:
        auth_header = request.headers.get("Authorization", "")
        supplied: Optional[str] = None
        if auth_header.startswith("Bearer "):
            supplied = auth_header[7:]
        else:
            supplied = request.query.get("token")
        if supplied != required_token:
            return web.Response(status=401, text="Unauthorized")

    # Connection limiting
    app = request.app
    app["metrics"]["attempts"] += 1
    if app.get("current_connections", 0) >= app.get("max_connections", 50):
        app["metrics"]["rejected"] += 1
        return web.Response(status=503, text="Too many connections")
    app["current_connections"] = app.get("current_connections", 0) + 1
    app["metrics"]["accepted"] += 1

    # Prepare SSE response
    headers = {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
    }
    resp = web.StreamResponse(status=200, headers=headers)
    await resp.prepare(request)

    proc: Optional[asyncio.subprocess.Process] = None

    async def write_event(data: str) -> None:
        payload = f"data: {data}\n\n".encode()
        await resp.write(payload)
        await resp.drain()

    async def read_body_and_send() -> None:
        try:
            assert proc is not None and proc.stdin is not None
            body = await request.content.read()  # for SSE, handle single-shot request body
            if body:
                proc.stdin.write(body)
                await proc.stdin.drain()
            if not proc.stdin.is_closing():
                proc.stdin.close()
        except asyncio.CancelledError:
            raise
        except Exception as e:
            logger.error(f"read_body_and_send failed: {e}")

    async def proc_to_sse() -> None:
        try:
            assert proc is not None and proc.stdout is not None
            while True:
                line = await proc.stdout.readline()
                if not line:
                    break
                await write_event(line.decode(errors="ignore").rstrip("\n"))
        except asyncio.CancelledError:
            raise
        except Exception as e:
            logger.error(f"proc_to_sse failed: {e}")

    async def stderr_logger() -> None:
        try:
            assert proc is not None and proc.stderr is not None
            while True:
                line = await proc.stderr.readline()
                if not line:
                    break
                logger.error(line.decode(errors="ignore").rstrip())
        except asyncio.CancelledError:
            raise
        except Exception as e:
            logger.error(f"stderr_logger failed: {e}")

    try:
        proc = await spawn_mcp_process()
        t_in = asyncio.create_task(read_body_and_send())
        t_out = asyncio.create_task(proc_to_sse())
        t_err = asyncio.create_task(stderr_logger())
        done, pending = await asyncio.wait({t_in, t_out}, return_when=asyncio.FIRST_COMPLETED)
        for t in pending:
            t.cancel()
        await asyncio.gather(*pending, return_exceptions=True)
        t_err.cancel()
        await asyncio.gather(t_err, return_exceptions=True)
    finally:
        try:
            await resp.write_eof()
        except Exception:
            pass
        if proc is not None:
            try:
                if proc.stdin and not proc.stdin.is_closing():
                    proc.stdin.close()
                try:
                    await asyncio.wait_for(proc.wait(), timeout=2)
                except asyncio.TimeoutError:
                    proc.kill()
            except Exception:
                pass
        if app.get("current_connections"):
            app["current_connections"] -= 1

    return resp


async def create_app() -> web.Application:
    app = web.Application()
    # Initialize connection counters and metrics
    app["current_connections"] = 0
    app["max_connections"] = int(os.getenv("GATEWAY_MAX_CONNECTIONS", "50"))
    app["metrics"] = {"attempts": 0, "accepted": 0, "rejected": 0}

    app.router.add_post("/", http_stream_handler)
    app.router.add_get("/ws", ws_handler)
    app.router.add_get("/healthz", healthz)
    app.router.add_get("/metrics", metrics)
    app.router.add_post("/sse", sse_handler)
    return app


async def main() -> None:
    host = os.getenv("GATEWAY_HOST", "0.0.0.0")
    port = int(os.getenv("GATEWAY_PORT", "3000"))
    logger.info(f"Starting MCP WebSocket gateway on {host}:{port}")
    app = await create_app()
    runner = web.AppRunner(app)
    await runner.setup()
    site = web.TCPSite(runner, host=host, port=port)
    await site.start()
    try:
        while True:
            await asyncio.sleep(3600)
    except asyncio.CancelledError:
        pass
    finally:
        await runner.cleanup()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Gateway shutdown requested")
