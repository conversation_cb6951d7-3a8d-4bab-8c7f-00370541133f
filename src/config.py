from __future__ import annotations

import os
from dataclasses import dataclass


@dataclass(frozen=True)
class HttpConfig:
    base_url: str = os.getenv("OURTRIP_BASE_URL", "https://api-hml.ourtrip.com.br").rstrip("/")
    timeout_total: int = int(os.getenv("OURTRIP_HTTP_TIMEOUT_TOTAL", "30"))
    timeout_connect: int = int(os.getenv("OURTRIP_HTTP_TIMEOUT_CONNECT", "10"))
    ssl_verify: bool = os.getenv("OURTRIP_SSL_VERIFY", "true").lower() in {"1", "true", "yes", "on"}
    max_retries: int = int(os.getenv("OURTRIP_HTTP_MAX_RETRIES", "3"))


HTTP_CONFIG = HttpConfig()

